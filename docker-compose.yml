version: '3.8'

services:
  # PostgreSQL databáze
  postgres:
    image: postgres:15-alpine
    container_name: pujcovna_postgres
    environment:
      POSTGRES_DB: pujcovna
      POSTGRES_USER: pujcovna
      POSTGRES_PASSWORD: pujcovna_password
    ports:
      - "15432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - pilarj-proxy_proxyNet

  # Redis cache
  redis:
    image: redis:7-alpine
    container_name: pujcovna_redis
    volumes:
      - redis_data:/data
    networks:
      - pilarj-proxy_proxyNet

  # PHP/Symfony backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: pujcovna_backend
    volumes:
      - ./backend:/var/www/html
      - ./backend/var:/var/www/html/var
      - ./backend/vendor:/var/www/html/vendor
      - ./.env:/var/www/html/.env
    env_file:
      - .env
    depends_on:
      - postgres
      - redis
    networks:
      - pilarj-proxy_proxyNet

  # Next.js frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: pujcovna_frontend
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
      - ./.env:/app/.env
    env_file:
      - .env
    depends_on:
      - backend
    networks:
      - pilarj-proxy_proxyNet

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: pujcovna_nginx
    ports:
      - "18080:80"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - frontend
      - backend
    networks:
      - pilarj-proxy_proxyNet

volumes:
  postgres_data:
  redis_data:

networks:
  pilarj-proxy_proxyNet:
    external: true
