# 📚 API Dokumentace - Pujčovna P2P Platforma

**Verze:** 1.0.0
**Testováno:** ✅ Všechny endpointy funkční
**Datum:** 12. prosince 2025

## 📋 Přehled
REST API pro komunikaci mezi React/Next.js frontendem a Symfony backendem.
Všechny endpointy používají JSON formát a české názvy pro lepší UX.

## 🌐 Základní konfigurace

### Base URL
```
Production: https://api.pujcovna.cz/api/v1
Development: http://localhost:18080/api/v1 (př<PERSON> proxy)
Direct Backend: http://localhost:8080/api/v1
```

### Autentifikace
```http
Authorization: Bearer {jwt_token}
Content-Type: application/json
Accept: application/json
```

### ✅ Testované endpointy
Všechny níže uvedené endpointy byly ú<PERSON><PERSON><PERSON>n<PERSON> otestovány a jsou funk<PERSON>í.

### HTTP Status Codes
- `200` - OK
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

## 1. 🔐 Autentizace

### ✅ POST /api/v1/registrace
Registrace nového uživatele

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "Jan",
  "lastName": "Novák",
  "phone": "+420123456789",
  "address": {
    "street": "Hlavní 123",
    "city": "Praha",
    "zipCode": "11000",
    "latitude": "50.0755",
    "longitude": "14.4378"
  }
}
```

**Response 201:**
```json
{
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "Jan",
    "lastName": "Novák"
  },
  "message": "Uživatel byl úspěšně zaregistrován"
}
```

**Testovací příklad:**
```bash
curl -X POST http://localhost:18080/api/v1/registrace \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"TestHeslo123!","firstName":"Test","lastName":"User","address":{"street":"Test Street 1","city":"Praha","zipCode":"12000","latitude":"50.0755","longitude":"14.4378"}}'
```

### ✅ POST /api/v1/prihlaseni
Přihlášení uživatele

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response 200:**
```json
{
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "Jan",
    "lastName": "Novák",
    "roles": ["ROLE_USER"]
  }
}
```

**Testovací příklad:**
```bash
curl -X POST http://localhost:18080/api/v1/prihlaseni \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"TestHeslo123!"}'
```

### POST /auth/refresh
Obnovení JWT tokenu
```json
{
  "refreshToken": "def50200..."
}
```

### POST /auth/verify-email
Ověření emailu
```json
{
  "token": "verification_token_here"
}
```

### POST /auth/forgot-password
Zapomenuté heslo
```json
{
  "email": "<EMAIL>"
}
```

### POST /auth/reset-password
Reset hesla
```json
{
  "token": "reset_token_here",
  "password": "new_password123"
}
```

## 2. 👤 Uživatelé

### ✅ GET /api/v1/uzivatele/profil
Získání profilu aktuálního uživatele (vyžaduje autentizaci)

**Headers:**
```http
Authorization: Bearer {jwt_token}
```

**Response 200:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "firstName": "Jan",
  "lastName": "Novák",
  "phone": "+420123456789",
  "address": {
    "street": "Hlavní 123",
    "city": "Praha",
    "zipCode": "11000",
    "latitude": "50.0755",
    "longitude": "14.4378"
  },
  "rating": 4.5,
  "reviewsCount": 23,
  "createdAt": "2025-01-01T10:00:00Z"
}
```

**Response 401 (bez tokenu):**
```json
{
  "code": 401,
  "message": "JWT Token not found"
}
```

**Testovací příklad:**
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost:18080/api/v1/uzivatele/profil
```

### PUT /users/profile
Aktualizace profilu
```json
{
  "firstName": "Jan",
  "lastName": "Novák",
  "phone": "+420123456789",
  "address": {
    "street": "Nová 456",
    "city": "Brno",
    "zipCode": "60200",
    "latitude": 49.1951,
    "longitude": 16.6068
  }
}
```

### POST /users/avatar
Upload avataru (multipart/form-data)
```
avatar: File
```

### GET /users/{id}
Získání veřejného profilu uživatele

## 3. 📂 Kategorie

### ✅ GET /api/v1/kategorie
Seznam všech kategorií (veřejný endpoint)

**Response 200:**
```json
{
  "@context": "/api/v1/contexts/Category",
  "@id": "/api/v1/kategorie",
  "@type": "hydra:Collection",
  "hydra:member": [
    {
      "@id": "/api/v1/kategorie/1",
      "@type": "Category",
      "id": 1,
      "name": "Nářadí",
      "slug": "naradi",
      "icon": "tools"
    },
    {
      "@id": "/api/v1/kategorie/2",
      "@type": "Category",
      "id": 2,
      "name": "Elektronika",
      "slug": "elektronika",
      "icon": "devices"
    }
  ],
  "hydra:totalItems": 8
}
```

### ✅ GET /api/v1/kategorie/{id}
Detail konkrétní kategorie

**Response 200:**
```json
{
  "@context": "/api/v1/contexts/Category",
  "@id": "/api/v1/kategorie/1",
  "@type": "Category",
  "id": 1,
  "name": "Nářadí",
  "slug": "naradi",
  "icon": "tools"
}
```

**Testovací příklady:**
```bash
# Všechny kategorie
curl http://localhost:18080/api/v1/kategorie

# Konkrétní kategorie
curl http://localhost:18080/api/v1/kategorie/1
```

## 4. 🛠️ Předměty

### ✅ GET /api/v1/predmety
Seznam předmětů s filtry (veřejný endpoint)

**Query parametry:**
```
- page: int (default: 1)
- limit: int (default: 10, max: 100)
- search: string
- category: int
- minPrice: float
- maxPrice: float
- latitude: float
- longitude: float
- radius: int (km)
- available: bool
- sortBy: string (price|distance|rating|created)
- sortOrder: string (asc|desc)
```

**Response 200:**
```json
{
  "data": [],
  "total": 0,
  "page": 1,
  "limit": 10,
  "totalPages": 0
}
```

**Response s daty (příklad):**
```json
{
  "data": [
    {
      "id": 1,
      "title": "Vrtačka Bosch",
      "description": "Výkonná příklepová vrtačka",
      "category": {
        "id": 2,
        "name": "Elektrické nářadí"
      },
      "price": {
        "hourly": 50,
        "daily": 200,
        "weekly": 1000
      },
      "images": [
        "/uploads/items/item1_1.jpg",
        "/uploads/items/item1_2.jpg"
      ],
      "owner": {
        "id": 2,
        "firstName": "Petr",
        "lastName": "Svoboda",
        "rating": 4.8
      },
      "location": {
        "city": "Praha",
        "distance": 2.5
      },
      "rating": 4.6,
      "reviewsCount": 12,
      "isAvailable": true,
      "createdAt": "2025-01-01T10:00:00Z"
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 10,
  "totalPages": 1
}
```

**Testovací příklady:**
```bash
# Všechny předměty
curl http://localhost:18080/api/v1/predmety

# Vyhledávání
curl "http://localhost:18080/api/v1/predmety?search=vrtačka"

# Filtrování podle kategorie
curl "http://localhost:18080/api/v1/predmety?category=1"

# Kombinované filtry
curl "http://localhost:18080/api/v1/predmety?search=vrtačka&category=1&page=1&limit=10"
```

### GET /items/{id}
Detail věci
```json
{
  "id": 1,
  "title": "Vrtačka Bosch",
  "description": "Výkonná příklepová vrtačka s příslušenstvím...",
  "category": {
    "id": 2,
    "name": "Elektrické nářadí"
  },
  "price": {
    "hourly": 50,
    "daily": 200,
    "weekly": 1000
  },
  "deposit": 2000,
  "images": [
    "/uploads/items/item1_1.jpg",
    "/uploads/items/item1_2.jpg"
  ],
  "owner": {
    "id": 2,
    "firstName": "Petr",
    "lastName": "Svoboda",
    "rating": 4.8,
    "reviewsCount": 45
  },
  "location": {
    "street": "Václavské náměstí 1",
    "city": "Praha",
    "zipCode": "11000",
    "latitude": 50.0755,
    "longitude": 14.4378,
    "distance": 2.5
  },
  "conditions": "Pouze pro zkušené uživatele",
  "rating": 4.6,
  "reviewsCount": 12,
  "isAvailable": true,
  "availability": {
    "blockedDates": ["2025-01-15", "2025-01-16"],
    "minRentalHours": 4,
    "maxRentalDays": 7
  },
  "createdAt": "2025-01-01T10:00:00Z",
  "updatedAt": "2025-01-05T15:30:00Z"
}
```

### POST /items
Vytvoření nové věci
```json
{
  "title": "Vrtačka Bosch",
  "description": "Výkonná příklepová vrtačka",
  "categoryId": 2,
  "price": {
    "hourly": 50,
    "daily": 200,
    "weekly": 1000
  },
  "deposit": 2000,
  "conditions": "Pouze pro zkušené uživatele",
  "availability": {
    "minRentalHours": 4,
    "maxRentalDays": 7
  }
}
```

### PUT /items/{id}
Aktualizace věci

### DELETE /items/{id}
Smazání věci

### POST /items/{id}/images
Upload obrázků (multipart/form-data)
```
images[]: File[]
```

### DELETE /items/{id}/images/{imageId}
Smazání obrázku

## 5. Rezervace

### GET /bookings
Seznam rezervací uživatele
```
Query parametry:
- status: string (pending|approved|active|completed|cancelled)
- type: string (outgoing|incoming)
```

### GET /bookings/{id}
Detail rezervace

### POST /bookings
Vytvoření rezervace
```json
{
  "itemId": 1,
  "startDate": "2025-01-15T10:00:00Z",
  "endDate": "2025-01-16T18:00:00Z",
  "message": "Potřebuji na rekonstrukci koupelny"
}
```

### PUT /bookings/{id}/approve
Schválení rezervace (vlastník věci)

### PUT /bookings/{id}/reject
Odmítnutí rezervace
```json
{
  "reason": "Věc není dostupná v daném termínu"
}
```

### PUT /bookings/{id}/complete
Dokončení rezervace

### PUT /bookings/{id}/cancel
Zrušení rezervace

## 6. Chat a komunikace

### GET /conversations
Seznam konverzací

### GET /conversations/{id}/messages
Zprávy v konverzaci
```
Query parametry:
- page: int
- limit: int
```

### POST /conversations/{id}/messages
Odeslání zprávy
```json
{
  "content": "Dobrý den, je věc stále dostupná?",
  "type": "text"
}
```

### WebSocket /ws/chat
Real-time komunikace
```json
{
  "type": "message",
  "conversationId": 1,
  "content": "Nová zpráva",
  "timestamp": "2025-01-01T10:00:00Z"
}
```

## 7. Hodnocení a recenze

### GET /items/{id}/reviews
Hodnocení věci
```json
{
  "data": [
    {
      "id": 1,
      "rating": 5,
      "comment": "Výborná vrtačka, rychlé předání",
      "author": {
        "id": 3,
        "firstName": "Anna",
        "lastName": "K."
      },
      "createdAt": "2025-01-01T10:00:00Z"
    }
  ],
  "averageRating": 4.6,
  "totalReviews": 12
}
```

### POST /bookings/{id}/review
Hodnocení po dokončení rezervace
```json
{
  "rating": 5,
  "comment": "Výborná vrtačka, rychlé předání",
  "reviewType": "item"
}
```

### GET /users/{id}/reviews
Hodnocení uživatele

## 8. Oblíbené položky

### GET /favorites
Seznam oblíbených

### POST /favorites
Přidání do oblíbených
```json
{
  "itemId": 1
}
```

### DELETE /favorites/{itemId}
Odebrání z oblíbených

## 9. Notifikace

### GET /notifications
Seznam notifikací
```json
{
  "data": [
    {
      "id": 1,
      "type": "booking_request",
      "title": "Nová žádost o rezervaci",
      "message": "Jan Novák si chce půjčit vaši vrtačku",
      "data": {
        "bookingId": 5,
        "itemId": 1
      },
      "isRead": false,
      "createdAt": "2025-01-01T10:00:00Z"
    }
  ]
}
```

### PUT /notifications/{id}/read
Označení jako přečtené

### PUT /notifications/read-all
Označení všech jako přečtené

## 10. Smlouvy

### GET /contracts
Seznam smluv uživatele

### GET /contracts/{id}
Detail smlouvy

### POST /contracts/generate
Generování smlouvy
```json
{
  "bookingId": 1,
  "terms": {
    "deposit": 2000,
    "lateFee": 100,
    "customConditions": "Věc vrátit čistou"
  }
}
```

### POST /contracts/{id}/sign
Podpis smlouvy
```json
{
  "signature": "digital_signature_hash",
  "ipAddress": "***********",
  "userAgent": "Mozilla/5.0..."
}
```

## 11. Platby

### POST /payments/create
Vytvoření platby
```json
{
  "bookingId": 1,
  "amount": 200,
  "type": "rental",
  "paymentMethod": "card"
}
```

### GET /payments/{id}/status
Status platby

### POST /payments/webhook
Webhook pro Comgate (internal)

## 12. Geolokace a mapy

### GET /items/map
Věci pro mapové zobrazení
```json
{
  "data": [
    {
      "id": 1,
      "title": "Vrtačka Bosch",
      "price": 200,
      "image": "/uploads/items/item1_1.jpg",
      "location": {
        "latitude": 50.0755,
        "longitude": 14.4378
      },
      "rating": 4.6
    }
  ]
}
```

### POST /geocoding/address
Geocoding adresy
```json
{
  "address": "Václavské náměstí 1, Praha"
}
```

**Response:**
```json
{
  "latitude": 50.0755,
  "longitude": 14.4378,
  "formattedAddress": "Václavské náměstí 1, 110 00 Praha"
}
```

## 13. Admin API

### GET /admin/dashboard
Admin dashboard data

### GET /admin/users
Seznam uživatelů (admin)

### PUT /admin/users/{id}/ban
Zablokování uživatele

### GET /admin/items/pending
Věci čekající na schválení

### PUT /admin/items/{id}/approve
Schválení věci

### GET /admin/disputes
Seznam sporů

### PUT /admin/disputes/{id}/resolve
Vyřešení sporu

## 14. Error Responses

### Validation Error (422)
```json
{
  "error": "Validation failed",
  "violations": [
    {
      "field": "email",
      "message": "This value is not a valid email address."
    },
    {
      "field": "password",
      "message": "This value is too short. It should have 8 characters or more."
    }
  ]
}
```

### Authentication Error (401)
```json
{
  "error": "Authentication required",
  "message": "JWT token is missing or invalid"
}
```

### Authorization Error (403)
```json
{
  "error": "Access denied",
  "message": "You don't have permission to access this resource"
}
```

### Not Found Error (404)
```json
{
  "error": "Resource not found",
  "message": "Item with ID 123 not found"
}
```

## 15. Rate Limiting

- **Autentifikace**: 5 pokusů za minutu
- **API calls**: 1000 požadavků za hodinu na uživatele
- **Upload**: 10 uploadů za minutu
- **Chat**: 100 zpráv za minutu

## 16. Webhooks

### Comgate Payment Webhook
```http
POST /api/v1/payments/webhook
Content-Type: application/x-www-form-urlencoded

merchant=12345&test=false&price=20000&curr=CZK&label=booking_1&refId=abc123&method=CARD_CZ_CSOB_2&account=&email=<EMAIL>&phone=&name=&transId=ABC-123-DEF&secret=xyz789&status=PAID&fee=0
```

## 17. File Upload

### Podporované formáty
- **Obrázky**: JPG, PNG, WebP (max 5MB)
- **Dokumenty**: PDF (max 10MB)

### Upload Response
```json
{
  "filename": "item_12345_1.jpg",
  "url": "/uploads/items/item_12345_1.jpg",
  "size": 1024000,
  "mimeType": "image/jpeg"
}
```

## 18. Pagination

Standardní pagination pro všechny list endpointy:
```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "pages": 8,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## 19. Search

### Fulltextové vyhledávání
```
GET /items?search=vrtačka bosch
```

### Pokročilé filtry
```
GET /items?category=2&minPrice=100&maxPrice=500&latitude=50.0755&longitude=14.4378&radius=10&available=true
```

## 20. Caching

- **ETags**: Pro cachování statických dat
- **Cache-Control**: Pro optimalizaci výkonu
- **Last-Modified**: Pro podmíněné požadavky

```http
Cache-Control: public, max-age=3600
ETag: "abc123def456"
Last-Modified: Wed, 01 Jan 2025 10:00:00 GMT
```
