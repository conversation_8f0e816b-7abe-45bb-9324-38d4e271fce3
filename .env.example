# ===========================================
# PUJČOVNA - Environment Variables Example
# ===========================================
# Zkopírujte tento soubor jako .env a upravte hodnoty podle potřeby

# ===========================================
# SYMFONY BACKEND
# ===========================================

# Symfony prostředí
APP_ENV=dev
APP_SECRET=your-secret-key-here

# Databáze
DATABASE_URL="*****************************************************/pujcovna?serverVersion=15&charset=utf8"

# Redis
REDIS_URL=redis://redis:6379

# JWT
JWT_SECRET_KEY=%kernel.project_dir%/config/jwt/private.pem
JWT_PUBLIC_KEY=%kernel.project_dir%/config/jwt/public.pem
JWT_PASSPHRASE=your-jwt-passphrase

# Mailer
MAILER_DSN=null://null

# API Platform
API_PLATFORM_TITLE="Pujčovna API"
API_PLATFORM_DESCRIPTION="P2P sharing platform API"
API_PLATFORM_VERSION="1.0.0"

# CORS
CORS_ALLOW_ORIGIN=^https?://(localhost|127\.0\.0\.1)(:[0-9]+)?$

# Upload paths
UPLOAD_PATH=/uploads
UPLOAD_BASE_URL=http://localhost:8000

# ===========================================
# NEXT.JS FRONTEND
# ===========================================

# API URL pro backend (přes Nginx)
# Poznámka: Používáme Nominatim API (OpenStreetMap) pro našeptávač adres
# Google Maps API už není potřeba - odstraněno kvůli chybě ApiTargetBlockedMapError
NEXT_PUBLIC_API_URL=http://localhost:18080/api/v1

# Node.js prostředí
NODE_ENV=development

# ===========================================
# DOCKER COMPOSE
# ===========================================

# Pro lokální vývoj používejte výše uvedené hodnoty
# Pro produkci vytvořte .env.server s produkčními hodnotami
