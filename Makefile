.PHONY: help build up down restart logs shell-backend shell-frontend install-backend install-frontend test-backend test-frontend clean setup

# Vý<PERSON>zí cíl
help:
	@echo "🏠 Pujčovna - P2P Sharing Platform"
	@echo ""
	@echo "Dostupné příkazy:"
	@echo ""
	@echo "Development:"
	@echo "  setup              - Kompletní setup projektu (Docker)"
	@echo "  build              - Sestaví všechny Docker kontejnery"
	@echo "  up                 - Spustí aplikaci"
	@echo "  down               - Zastaví aplikaci"
	@echo "  restart            - Restartuje aplikaci"
	@echo "  logs               - Zobrazí logy všech služeb"
	@echo ""
	@echo "Databáze:"
	@echo "  migrate            - Spustí databázové migrace"
	@echo "  migrate-diff       - Vytvoří novou migraci"
	@echo "  fixtures           - Načte testovací data"
	@echo ""
	@echo "JWT klíče:"
	@echo "  jwt-keys           - Vygeneruje JWT klíče pro autentizaci"
	@echo "  jwt-keys-server    - Vygeneruje JWT klíče pro server (bez Docker)"
	@echo ""
	@echo "Utility:"
	@echo "  shell-backend      - Otevře shell v backend kontejneru"
	@echo "  shell-frontend     - Otevře shell v frontend kontejneru"
	@echo "  test-backend       - Spustí backend testy"
	@echo "  test-frontend      - Spustí frontend testy"
	@echo "  clean              - Vyčistí Docker cache a volumes"
	@echo ""
	@echo "Testování aplikace:"
	@echo "  test-api           - Testuje API endpointy"
	@echo "  health             - Zkontroluje zdraví všech služeb"
	@echo "  test-all           - Spustí všechny testy"
	@echo ""
	@echo "Kontrola standardů kódu:"
	@echo "  lint               - Kontrola standardů (frontend + backend)"
	@echo "  lint-frontend      - Kontrola standardů frontendu"
	@echo "  lint-backend       - Kontrola standardů backendu"
	@echo "  fix                - Oprava standardů (frontend + backend)"
	@echo "  fix-frontend       - Oprava standardů frontendu"
	@echo "  fix-backend        - Oprava standardů backendu"

# Docker příkazy
build:
	docker compose build

up:
	docker compose up -d

down:
	docker compose down

restart:
	docker compose restart

logs:
	docker compose logs -f

logs-backend:
	docker compose logs -f backend

logs-frontend:
	docker compose logs -f frontend

# Shell přístup
shell-backend:
	docker compose exec backend sh

shell-frontend:
	docker compose exec frontend sh

# Instalace závislostí
install-backend:
	docker compose exec backend composer install

install-frontend:
	docker compose exec frontend npm install

# Databáze
migrate:
	docker compose exec backend php bin/console doctrine:migrations:migrate --no-interaction

migrate-diff:
	docker compose exec backend php bin/console doctrine:migrations:diff

fixtures:
	docker compose exec backend php bin/console doctrine:fixtures:load --no-interaction

# JWT klíče
jwt-keys:
	@echo "🔑 Generování JWT klíčů v Docker kontejneru..."
	docker compose exec backend mkdir -p config/jwt
	docker compose exec backend php bin/console lexik:jwt:generate-keypair --skip-if-exists
	docker compose exec backend chmod 600 config/jwt/private.pem
	docker compose exec backend chmod 644 config/jwt/public.pem
	@echo "✅ JWT klíče byly vygenerovány!"

jwt-keys-server:
	@echo "🔑 Generování JWT klíčů pro server (bez Docker)..."
	@echo "Spusť tyto příkazy na serveru v backend adresáři:"
	@echo ""
	@echo "mkdir -p config/jwt"
	@echo "php bin/console lexik:jwt:generate-keypair --skip-if-exists"
	@echo "chmod 600 config/jwt/private.pem"
	@echo "chmod 644 config/jwt/public.pem"
	@echo "php bin/console cache:clear --env=prod"
	@echo ""
	@echo "Nebo manuálně:"
	@echo "openssl genpkey -out config/jwt/private.pem -aes256 -algorithm rsa -pkcs8 -pass pass:your-jwt-passphrase"
	@echo "openssl pkey -in config/jwt/private.pem -passin pass:your-jwt-passphrase -out config/jwt/public.pem -pubout"

# Testování
test-backend:
	docker compose exec backend php bin/phpunit

test-frontend:
	docker compose exec frontend npm test

# Utility
clean:
	docker compose down -v
	docker system prune -f
	docker volume prune -f

# Kompletní setup
setup: build up install-backend install-frontend migrate fixtures
	@echo ""
	@echo "✅ Aplikace je připravena!"
	@echo ""
	@echo "🌐 Dostupné URL:"
	@echo "   Frontend (Next.js): http://localhost:3000"
	@echo "   Backend (Symfony): http://localhost:8000"
	@echo "   Nginx proxy: http://localhost:18080"
	@echo ""
	@echo "🗄️ Databáze:"
	@echo "   PostgreSQL: localhost:5432"
	@echo "   Redis: localhost:6379"

# Testování API a aplikace
test-api:
	@echo "🔍 Testování API endpointů..."
	@echo "Testování kategorií..."
	@curl -s http://localhost:18080/api/v1/kategorie | head -n 5
	@echo "\nTestování předmětů..."
	@curl -s http://localhost:18080/api/v1/predmety | head -n 5
	@echo "\nTestování vyhledávání..."
	@curl -s "http://localhost:18080/api/v1/predmety?search=test" | head -n 5

health:
	@echo "🏥 Kontrola zdraví služeb..."
	@echo "Docker kontejnery:"
	@docker compose ps
	@echo "\nNginx proxy:"
	@echo -n "  http://localhost:18080: "
	@curl -s -o /dev/null -w "%{http_code}\n" http://localhost:18080
	@echo "Backend API:"
	@echo -n "  http://localhost:18080/api/v1/kategorie: "
	@curl -s -o /dev/null -w "%{http_code}\n" http://localhost:18080/api/v1/kategorie
	@echo "Frontend:"
	@echo -n "  http://localhost:13000: "
	@curl -s -o /dev/null -w "%{http_code}\n" http://localhost:13000

test-all: test-api health
	@echo "✅ Všechny testy dokončeny!"

# Kontrola standardů kódu
lint: lint-frontend lint-backend
	@echo "✅ Kontrola standardů dokončena!"

lint-frontend:
	@echo "🔍 Kontrola standardů frontendu..."
	docker compose exec frontend npm run lint
	docker compose exec frontend npm run format:check
	docker compose exec frontend npm run type-check

lint-backend:
	@echo "🔍 Kontrola standardů backendu..."
	docker compose exec backend composer validate --strict
	docker compose exec backend composer cs-check
	docker compose exec backend composer phpstan

fix: fix-frontend fix-backend
	@echo "✅ Oprava standardů dokončena!"

fix-frontend:
	@echo "🔧 Oprava standardů frontendu..."
	docker compose exec frontend npm run lint:fix
	docker compose exec frontend npm run format

fix-backend:
	@echo "🔧 Oprava standardů backendu..."
	docker compose exec backend composer cs-fix
