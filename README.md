# Pujčovna - P2P Sharing Platform

Moderní platforma pro půjčování věcí mezi sousedy. Umožňuje uživatelům jednoduše půjčovat a pronajímat věci v rámci svého okolí.

## 🚀 Technologie

### Backend
- **PHP 8.2+** s **Symfony 7.0**
- **PostgreSQL** databáze
- **Redis** pro cache (volitelné)
- **API Platform** pro REST API
- **JWT** autentifikace
- **Docker** kontejnerizace

### Frontend
- **Next.js 14** s **TypeScript**
- **Tailwind CSS** pro styling
- **React Query** pro state management
- **React Hook Form** pro formuláře
- **PWA** připravenost

## 📋 Požadavky

- Docker (s Docker Compose v2)
- Make (volitelné, pro pohodlnější správu)

## 🛠️ Instalace a spuštění

### PHP Hosting (doporučeno pro jednoduché hostingy)

```bash
# Klonování repozitáře
git clone https://github.com/PilarJ/pujcovna.git
cd pujcovna

# Příprava pro PHP hosting
make setup-hosting
```

**Po dokončení:**
1. Nahraj obsah `deploy-package/` na hosting (složka se generuje automaticky)
2. Nastav document root na `public/` složku (nebo použij `.htaccess` přesměrování)
3. Uprav `.env` soubor s databázovým připojením
4. Spusť migrace: `php bin/console doctrine:migrations:migrate`

> **Poznámka:** Složka `deploy-package/` je v `.gitignore` a generuje se automaticky při buildu. Obsahuje také `.htaccess` soubory pro hostingy bez možnosti nastavit document root.

**Aplikace bude dostupná na vaší doméně:**
- 🌐 **Frontend**: https://yourdomain.com
- 🔌 **API**: https://yourdomain.com/api/v1
- 📁 **Uploads**: https://yourdomain.com/uploads/

### Development prostředí

### 1. Klonování repozitáře
```bash
git clone https://github.com/PilarJ/pujcovna.git
cd pujcovna
```

### 2. Kompletní setup (doporučeno)
```bash
make setup
```

Tento příkaz:
- Sestaví všechny Docker kontejnery
- Spustí aplikaci
- Nainstaluje závislosti (backend i frontend)
- Spustí databázové migrace
- Načte testovací data

### 3. Manuální spuštění
```bash
# Sestavení kontejnerů
make build

# Spuštění aplikace
make up

# Instalace závislostí
make install-backend
make install-frontend

# Databázové migrace
make migrate
```

## 🌐 Přístup k aplikaci

Po úspěšném spuštění:

- **🎯 Hlavní aplikace**: http://localhost:18080 (doporučeno - Nginx proxy)
- **Frontend**: http://localhost:13000 (přímý přístup)
- **Backend API**: http://localhost:18080/api/v1 (přes Nginx proxy)
- **API dokumentace**: http://localhost:18080/api/v1/docs
- **PostgreSQL**: localhost:15432
- **Redis**: localhost:16379

### ✅ Ověření funkčnosti

Po spuštění můžete otestovat:

1. **Homepage**: http://localhost:18080
2. **Registrace**: http://localhost:18080/registrace
3. **Přihlášení**: http://localhost:18080/prihlaseni
4. **Vyhledávání**: http://localhost:18080/predmety
5. **API endpointy**:
   - Předměty: http://localhost:18080/api/v1/predmety
   - Kategorie: http://localhost:18080/api/v1/kategorie

## 🧪 Testování API

Základní testování API endpointů (všechny endpointy jsou dostupné přes Nginx proxy na portu 18080):

```bash
# Registrace nového uživatele
curl -X POST http://localhost:18080/api/v1/registrace \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","firstName":"Test","lastName":"User","address":{"street":"Test Street 1","city":"Praha","zipCode":"12000","latitude":"50.0755","longitude":"14.4378"}}'

# Přihlášení uživatele
curl -X POST http://localhost:18080/api/v1/prihlaseni \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Získání kategorií (veřejný endpoint)
curl http://localhost:18080/api/v1/kategorie

# Získání předmětů (veřejný endpoint)
curl http://localhost:18080/api/v1/predmety

# Vyhledávání předmětů s filtry
curl "http://localhost:18080/api/v1/predmety?search=vrtačka&category=1&page=1&limit=10"

# Získání profilu uživatele (vyžaduje JWT token)
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" http://localhost:18080/api/v1/uzivatele/profil
```

## 📚 Dostupné příkazy

```bash
# Základní příkazy
make help              # Zobrazí všechny dostupné příkazy
make up                # Spustí aplikaci
make down              # Zastaví aplikaci
make restart           # Restartuje aplikaci
make logs              # Zobrazí logy všech služeb

# Development
make shell-backend     # Otevře shell v backend kontejneru
make shell-frontend    # Otevře shell v frontend kontejneru
make migrate           # Spustí databázové migrace
make migrate-diff      # Vytvoří novou migraci
make fixtures          # Načte testovací data

# Testování
make test-backend      # Spustí backend testy
make test-frontend     # Spustí frontend testy

# Utility
make clean             # Vyčistí Docker cache a volumes
```

## ✅ Aktuální stav aplikace

### 🎉 PLNĚ FUNKČNÍ KOMPONENTY:

#### Infrastruktura:
- ✅ **Docker kontejnerizace** - Všechny služby běží v Docker kontejnerech
- ✅ **Nginx reverse proxy** - Jednotný vstupní bod na portu 18080
- ✅ **PostgreSQL databáze** - Plně funkční s migrací a daty
- ✅ **Redis cache** - Pro session a cache management

#### Backend (Symfony 7):
- ✅ **API Platform** - Automatická API dokumentace a validace
- ✅ **JWT autentizace** - Registrace a přihlášení uživatelů
- ✅ **Doctrine ORM** - Databázové entity a migrace
- ✅ **Validace** - Frontend i backend validace formulářů
- ✅ **CORS konfigurace** - Správné nastavení pro frontend-backend komunikaci

#### Frontend (Next.js 14):
- ✅ **React komponenty** - Plně funkční UI komponenty
- ✅ **TypeScript** - Type-safe development
- ✅ **Tailwind CSS** - Responsivní design
- ✅ **App Router** - Next.js 14 routing systém
- ✅ **Form handling** - Validace a odeslání formulářů

#### Uživatelské funkce:
- ✅ **Registrace uživatelů** - Kompletní formulář s adresou a souřadnicemi
- ✅ **Přihlašování** - JWT token autentizace
- ✅ **Dashboard** - Personalizovaný dashboard pro uživatele
- ✅ **Navigace** - Plně funkční menu a routing
- ✅ **Vyhledávání** - Vyhledávání předmětů s filtry
- ✅ **Kategorie** - Filtrování podle kategorií

### Implementované entity:
- ✅ **User** - Uživatelé s adresami a autentizací
- ✅ **Address** - Geografické adresy s souřadnicemi
- ✅ **Category** - 8 přednastavených kategorií
- ✅ **Item** - Předměty k půjčování (připraveno)
- ✅ **Booking** - Rezervace a půjčování (připraveno)
- ✅ **Review** - Hodnocení a recenze (připraveno)

### API endpointy (české URL):
- ✅ `POST /api/v1/registrace` - Registrace uživatele
- ✅ `POST /api/v1/prihlaseni` - Přihlášení uživatele
- ✅ `GET /api/v1/uzivatele/profil` - Profil uživatele (chráněný)
- ✅ `GET /api/v1/kategorie` - Seznam kategorií (veřejný)
- ✅ `GET /api/v1/predmety` - Seznam předmětů s vyhledáváním (veřejný)
- ✅ `POST /api/v1/predmety` - Vytvoření předmětu (chráněný)
- ✅ `GET /api/v1/uzivatele/predmety` - Moje předměty (chráněný)
- ✅ `GET /api/v1/pujcovani` - Seznam rezervací (chráněný)

## 🏗️ Struktura projektu

```
pujcovna/
├── backend/                 # Symfony backend
│   ├── src/
│   │   ├── Controller/     # API controllery
│   │   ├── Entity/         # Doctrine entity
│   │   ├── Repository/     # Databázové repository
│   │   └── Security/       # Bezpečnostní komponenty
│   ├── config/             # Symfony konfigurace
│   └── migrations/         # Databázové migrace
├── frontend/               # Next.js frontend
│   ├── app/               # Next.js 13+ app directory
│   ├── components/        # React komponenty
│   ├── hooks/             # Custom React hooks
│   ├── lib/               # Utility knihovny
│   └── types/             # TypeScript typy
├── docker/                # Docker konfigurace
│   ├── nginx/             # Nginx konfigurace
│   └── postgres/          # PostgreSQL init skripty
├── docker-compose.yml     # Docker Compose konfigurace
└── Makefile              # Automatizované příkazy
```

## 🔧 Vývoj

### Backend development
```bash
# Přístup do backend kontejneru
make shell-backend

# Vytvoření nové entity
php bin/console make:entity

# Vytvoření migrace
make migrate-diff

# Spuštění migrací
make migrate

# Načtení fixtures
make fixtures
```

### Frontend development
```bash
# Přístup do frontend kontejneru
make shell-frontend

# Instalace nového balíčku
npm install package-name

# Spuštění type checku
npm run type-check

# Spuštění linteru
npm run lint
```

## 📖 API dokumentace

Kompletní API dokumentace je dostupná na:
- **Swagger UI**: http://localhost:18000/api/v1/docs
- **Soubor**: [API_DOKUMENTACE.md](./API_DOKUMENTACE.md)

## 🎯 Implementační plán

### ✅ Fáze 1 - Základní struktura (HOTOVO)
- [x] Docker prostředí s Nginx proxy
- [x] Symfony 7 backend s API Platform
- [x] Next.js 14 frontend s TypeScript
- [x] PostgreSQL databáze s migrací
- [x] Základní entity a API endpointy
- [x] JWT autentizace (registrace + přihlášení)
- [x] Základní UI komponenty s Tailwind CSS
- [x] Kompletní uživatelský workflow
- [x] Vyhledávání a filtry předmětů
- [x] Kategorie systém

### 🚧 Fáze 2 - Rozšířené funkce (PŘIPRAVENO K IMPLEMENTACI)
- [ ] Správa předmětů (přidávání, editace, mazání)
- [ ] Nahrávání obrázků předmětů
- [ ] Rezervační systém s workflow
- [ ] Hodnocení a recenze
- [ ] Notifikace (email)
- [ ] Geografické vyhledávání

### 📅 Fáze 3 - Pokročilé funkce
- [ ] Chat komunikace mezi uživateli
- [ ] Platební systém (Stripe/PayPal)
- [ ] Generátor smluv (PDF)
- [ ] PWA funkcionalita
- [ ] Push notifikace
- [ ] Mobilní aplikace (React Native)

### 🎉 Aktuální stav: **APLIKACE JE PLNĚ FUNKČNÍ**

Základní funkcionalita je kompletně implementována a otestována:
- ✅ Registrace a přihlašování uživatelů
- ✅ Vyhledávání a procházení předmětů
- ✅ Kategorizace předmětů
- ✅ Responsivní design
- ✅ Bezpečné API s autentizací
- ✅ Kompletní infrastruktura v Dockeru

Detailní plán: [IMPLEMENTACNI_KROKY.md](./IMPLEMENTACNI_KROKY.md)

## 🤝 Přispívání

1. Forkněte repozitář
2. Vytvořte feature branch (`git checkout -b feature/nova-funkcionalita`)
3. Commitněte změny (`git commit -am 'Přidána nová funkcionalita'`)
4. Pushněte do branch (`git push origin feature/nova-funkcionalita`)
5. Vytvořte Pull Request

## 📄 Licence

Tento projekt je licencován pod MIT licencí - viz [LICENSE](LICENSE) soubor.

## 📞 Kontakt

- **Email**: <EMAIL>
- **GitHub**: [@PilarJ](https://github.com/PilarJ)

---

**Poznámka**: Toto je vývojová verze aplikace. Pro produkční nasazení je nutné upravit bezpečnostní nastavení a konfiguraci.
