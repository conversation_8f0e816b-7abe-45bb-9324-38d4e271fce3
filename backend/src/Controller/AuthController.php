<?php

namespace App\Controller;

use App\Entity\Address;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('/api/v1')]
class AuthController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private UserPasswordHasherInterface $passwordHasher,
        private ValidatorInterface $validator
    ) {
    }

    #[Route('/registrace', name: 'auth_register', methods: ['POST'])]
    public function register(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (!$data) {
            return new JsonResponse(['error' => 'Invalid JSON'], 400);
        }

        $user = new User();
        $user->setEmail($data['email'] ?? '');
        $user->setFirstName($data['firstName'] ?? '');
        $user->setLastName($data['lastName'] ?? '');
        $user->setPhone($data['phone'] ?? null);

        if (isset($data['password'])) {
            $hashedPassword = $this->passwordHasher->hashPassword($user, $data['password']);
            $user->setPassword($hashedPassword);
        }

        if (isset($data['address'])) {
            $address = new Address();
            $address->setStreet($data['address']['street'] ?? '');
            $address->setCity($data['address']['city'] ?? '');
            $address->setZipCode($data['address']['zipCode'] ?? '');
            $address->setLatitude($data['address']['latitude'] ?? '0');
            $address->setLongitude($data['address']['longitude'] ?? '0');
            $address->setUser($user);
            $user->setAddress($address);
        }

        $errors = $this->validator->validate($user);
        if (count($errors) > 0) {
            $errorMessages = [];
            foreach ($errors as $error) {
                $errorMessages[] = [
                    'field' => $error->getPropertyPath(),
                    'message' => $error->getMessage(),
                ];
            }

            return new JsonResponse(['error' => 'Validation failed', 'violations' => $errorMessages], 422);
        }

        try {
            $this->entityManager->persist($user);
            $this->entityManager->flush();

            return new JsonResponse([
                'user' => [
                    'id' => $user->getId(),
                    'email' => $user->getEmail(),
                    'firstName' => $user->getFirstName(),
                    'lastName' => $user->getLastName(),
                    'isVerified' => $user->isVerified(),
                ],
                'message' => 'User registered successfully',
            ], 201);
        } catch (\Exception) {
            return new JsonResponse(['error' => 'Registration failed'], 500);
        }
    }

    #[Route('/prihlaseni', name: 'auth_login', methods: ['POST'])]
    public function login(): JsonResponse
    {
        return new JsonResponse(['message' => 'Login handled by JWT bundle']);
    }

    #[Route('/uzivatele/profil', name: 'user_profile', methods: ['GET'])]
    public function getProfile(): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();

        if (!$user) {
            return new JsonResponse(['error' => 'User not found'], 404);
        }

        return new JsonResponse([
            'id' => $user->getId(),
            'email' => $user->getEmail(),
            'firstName' => $user->getFirstName(),
            'lastName' => $user->getLastName(),
            'phone' => $user->getPhone(),
            'avatar' => $user->getAvatar(),
            'rating' => $user->getRating(),
            'reviewsCount' => $user->getReviewsCount(),
            'isVerified' => $user->isVerified(),
            'createdAt' => $user->getCreatedAt()?->format('c'),
            'address' => $user->getAddress() ? [
                'id' => $user->getAddress()->getId(),
                'street' => $user->getAddress()->getStreet(),
                'city' => $user->getAddress()->getCity(),
                'zipCode' => $user->getAddress()->getZipCode(),
                'latitude' => (float) $user->getAddress()->getLatitude(),
                'longitude' => (float) $user->getAddress()->getLongitude(),
            ] : null,
        ]);
    }

    #[Route('/uzivatele/statistiky', name: 'user_stats', methods: ['GET'])]
    #[IsGranted('ROLE_USER')]
    public function getStats(): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();

        if (!$user) {
            return new JsonResponse(['error' => 'User not found'], 404);
        }

        // Počet předmětů uživatele
        $itemsCount = count($user->getItems());

        // Počet aktivních rezervací (jako půjčovatel)
        $activeBookingsCount = 0;
        $pendingBookingsCount = 0;
        $completedBookingsCount = 0;

        foreach ($user->getBookings() as $booking) {
            switch ($booking->getStatus()) {
                case 'active':
                case 'approved':
                    $activeBookingsCount++;

                    break;
                case 'pending':
                    $pendingBookingsCount++;

                    break;
                case 'completed':
                    $completedBookingsCount++;

                    break;
            }
        }

        // Počet rezervací předmětů uživatele (jako vlastník)
        $incomingBookingsCount = 0;
        foreach ($user->getItems() as $item) {
            foreach ($item->getBookings() as $booking) {
                if (in_array($booking->getStatus(), ['pending', 'approved', 'active'])) {
                    ++$incomingBookingsCount;
                }
            }
        }

        return new JsonResponse([
            'itemsCount' => $itemsCount,
            'activeBookingsCount' => $activeBookingsCount,
            'pendingBookingsCount' => $pendingBookingsCount,
            'completedBookingsCount' => $completedBookingsCount,
            'incomingBookingsCount' => $incomingBookingsCount,
            'totalEarnings' => 0, // TODO: implementovat výpočet výdělků
        ]);
    }

    #[Route('/uzivatele/aktivity', name: 'user_activities', methods: ['GET'])]
    #[IsGranted('ROLE_USER')]
    public function getActivities(): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();

        if (!$user) {
            return new JsonResponse(['error' => 'User not found'], 404);
        }

        $activities = [];

        // Přidej posledních 10 rezervací uživatele
        foreach ($user->getBookings() as $booking) {
            $activities[] = [
                'type' => 'booking_created',
                'message' => 'Nová rezervace pro "'.$booking->getItem()->getTitle().'"',
                'date' => $booking->getCreatedAt()->format('c'),
                'status' => $booking->getStatus(),
            ];
        }

        // Přidej rezervace předmětů uživatele
        foreach ($user->getItems() as $item) {
            foreach ($item->getBookings() as $booking) {
                $activities[] = [
                    'type' => 'booking_received',
                    'message' => 'Nová rezervace od '.$booking->getBorrower()->getFirstName().' '.substr($booking->getBorrower()->getLastName(), 0, 1).'.',
                    'date' => $booking->getCreatedAt()->format('c'),
                    'status' => $booking->getStatus(),
                ];
            }
        }

        // Seřaď podle data (nejnovější první) a vezmi pouze posledních 10
        usort($activities, function ($a, $b) {
            return strtotime($b['date']) - strtotime($a['date']);
        });

        return new JsonResponse(array_slice($activities, 0, 10));
    }
}
