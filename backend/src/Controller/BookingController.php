<?php

namespace App\Controller;

use App\Entity\Booking;
use App\Entity\User;
use App\Repository\BookingRepository;
use App\Repository\ItemRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/api/v1')]
class BookingController extends AbstractController
{
    public function __construct(
        private BookingRepository $bookingRepository,
        private ItemRepository $itemRepository,
        private EntityManagerInterface $entityManager
    ) {
    }

    #[Route('/pujcovani', name: 'booking_list', methods: ['GET'])]
    #[IsGranted('ROLE_USER')]
    public function list(Request $request): JsonResponse
    {
        $type = $request->query->get('type', 'outgoing'); // outgoing nebo incoming
        $status = $request->query->get('status');

        /** @var User $user */
        $user = $this->getUser();

        if ('incoming' === $type) {
            // Žádosti o moje předměty (jsem vlastník)
            $bookings = $this->bookingRepository->findByOwner($user);
        } else {
            // Moje žádosti (jsem žadatel)
            $bookings = $this->bookingRepository->findByUser($user);
        }

        // Filtrování podle statusu pokud je zadán
        if ($status) {
            $bookings = array_filter($bookings, function ($booking) use ($status) {
                return $booking->getStatus() === $status;
            });
        }

        // Převod na pole pro JSON response
        $data = [];
        foreach ($bookings as $booking) {
            $item = $booking->getItem();
            $borrower = $booking->getBorrower();

            if (!$item || !$borrower) {
                continue; // Přeskočíme neplatné rezervace
            }

            $owner = $item->getOwner();
            if (!$owner) {
                continue; // Přeskočíme předměty bez vlastníka
            }

            $data[] = [
                'id' => $booking->getId(),
                'item' => [
                    'id' => $item->getId(),
                    'title' => $item->getTitle(),
                    'dailyPrice' => $item->getDailyPrice(),
                    'owner' => [
                        'id' => $owner->getId(),
                        'firstName' => $owner->getFirstName(),
                        'lastName' => $owner->getLastName(),
                        'email' => $owner->getEmail(),
                    ],
                ],
                'borrower' => [
                    'id' => $borrower->getId(),
                    'firstName' => $borrower->getFirstName(),
                    'lastName' => $borrower->getLastName(),
                    'email' => $borrower->getEmail(),
                ],
                'startDate' => $booking->getStartDate()?->format('Y-m-d'),
                'endDate' => $booking->getEndDate()?->format('Y-m-d'),
                'status' => $booking->getStatus(),
                'totalPrice' => $booking->getTotalPrice(),
                'message' => $booking->getMessage(),
                'rejectionReason' => $booking->getRejectionReason(),
                'createdAt' => $booking->getCreatedAt()?->format('c'),
                'updatedAt' => $booking->getUpdatedAt()?->format('c'),
            ];
        }

        return new JsonResponse(['hydra:member' => $data]);
    }

    #[Route('/pujcovani', name: 'booking_create', methods: ['POST'])]
    #[IsGranted('ROLE_USER')]
    public function create(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (!$data) {
            return new JsonResponse(['error' => 'Invalid JSON'], 400);
        }

        // Validace povinných polí
        if (!isset($data['itemId']) || !isset($data['startDate']) || !isset($data['endDate'])) {
            return new JsonResponse(['error' => 'itemId, startDate a endDate jsou povinné'], 400);
        }

        // Najdi předmět
        $item = $this->itemRepository->find($data['itemId']);
        if (!$item) {
            return new JsonResponse(['error' => 'Předmět nebyl nalezen'], 404);
        }

        // Zkontroluj, že uživatel není vlastník předmětu
        if ($item->getOwner() === $this->getUser()) {
            return new JsonResponse(['error' => 'Nemůžete si půjčit vlastní předmět'], 400);
        }

        // Zkontroluj dostupnost předmětu
        if (!$item->isAvailable()) {
            return new JsonResponse(['error' => 'Předmět není dostupný'], 400);
        }

        try {
            $startDate = new \DateTimeImmutable($data['startDate']);
            $endDate = new \DateTimeImmutable($data['endDate']);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Neplatný formát data'], 400);
        }

        // Zkontroluj, že datum konce je po datu začátku
        if ($endDate <= $startDate) {
            return new JsonResponse(['error' => 'Datum konce musí být po datu začátku'], 400);
        }

        // Vypočítej celkovou cenu
        $diffDays = $startDate->diff($endDate)->days + 1;
        $totalPrice = $diffDays * (float) $item->getDailyPrice();

        // Vytvoř rezervaci
        $booking = new Booking();
        $booking->setItem($item);
        $booking->setBorrower($this->getUser());
        $booking->setStartDate($startDate);
        $booking->setEndDate($endDate);
        $booking->setMessage($data['message'] ?? null);
        $booking->setTotalPrice((string) $totalPrice);

        $this->entityManager->persist($booking);
        $this->entityManager->flush();

        // TODO: Poslat email notifikaci vlastníkovi

        return new JsonResponse([
            'id' => $booking->getId(),
            'status' => $booking->getStatus(),
            'totalPrice' => $booking->getTotalPrice(),
            'message' => 'Rezervace byla vytvořena',
        ], 201);
    }

    #[Route('/pujcovani/{id}/schvalit', name: 'booking_approve', methods: ['PUT'])]
    #[IsGranted('ROLE_USER')]
    public function approve(int $id): JsonResponse
    {
        $booking = $this->bookingRepository->find($id);

        if (!$booking) {
            return new JsonResponse(['error' => 'Rezervace nebyla nalezena'], 404);
        }

        // Zkontroluj, jestli je uživatel vlastník předmětu
        if ($booking->getItem()->getOwner() !== $this->getUser()) {
            return new JsonResponse(['error' => 'Nemáte oprávnění schválit tuto rezervaci'], 403);
        }

        if (Booking::STATUS_PENDING !== $booking->getStatus()) {
            return new JsonResponse(['error' => 'Rezervaci lze schválit pouze ve stavu čekající'], 400);
        }

        $booking->setStatus(Booking::STATUS_APPROVED);
        $booking->setUpdatedAt(new \DateTimeImmutable());

        $this->entityManager->flush();

        // TODO: Poslat email notifikaci žadateli

        return new JsonResponse([
            'id' => $booking->getId(),
            'status' => $booking->getStatus(),
            'message' => 'Rezervace byla schválena',
        ]);
    }

    #[Route('/pujcovani/{id}/zamitnout', name: 'booking_reject', methods: ['PUT'])]
    #[IsGranted('ROLE_USER')]
    public function reject(int $id, Request $request): JsonResponse
    {
        $booking = $this->bookingRepository->find($id);

        if (!$booking) {
            return new JsonResponse(['error' => 'Rezervace nebyla nalezena'], 404);
        }

        // Zkontroluj, jestli je uživatel vlastník předmětu
        if ($booking->getItem()->getOwner() !== $this->getUser()) {
            return new JsonResponse(['error' => 'Nemáte oprávnění zamítnout tuto rezervaci'], 403);
        }

        if (Booking::STATUS_PENDING !== $booking->getStatus()) {
            return new JsonResponse(['error' => 'Rezervaci lze zamítnout pouze ve stavu čekající'], 400);
        }

        $data = json_decode($request->getContent(), true);
        $reason = $data['reason'] ?? '';

        if (empty($reason)) {
            return new JsonResponse(['error' => 'Důvod zamítnutí je povinný'], 400);
        }

        $booking->setStatus(Booking::STATUS_REJECTED);
        $booking->setRejectionReason($reason);
        $booking->setUpdatedAt(new \DateTimeImmutable());

        $this->entityManager->flush();

        // TODO: Poslat email notifikaci žadateli

        return new JsonResponse([
            'id' => $booking->getId(),
            'status' => $booking->getStatus(),
            'rejectionReason' => $booking->getRejectionReason(),
            'message' => 'Rezervace byla zamítnuta',
        ]);
    }

    #[Route('/pujcovani/{id}/dokoncit', name: 'booking_complete', methods: ['PUT'])]
    #[IsGranted('ROLE_USER')]
    public function complete(int $id): JsonResponse
    {
        $booking = $this->bookingRepository->find($id);

        if (!$booking) {
            return new JsonResponse(['error' => 'Rezervace nebyla nalezena'], 404);
        }

        // Zkontroluj, jestli je uživatel vlastník předmětu nebo žadatel
        $user = $this->getUser();
        if ($booking->getItem()->getOwner() !== $user && $booking->getBorrower() !== $user) {
            return new JsonResponse(['error' => 'Nemáte oprávnění dokončit tuto rezervaci'], 403);
        }

        if (!in_array($booking->getStatus(), [Booking::STATUS_APPROVED, Booking::STATUS_ACTIVE])) {
            return new JsonResponse(['error' => 'Rezervaci lze dokončit pouze ve stavu schválená nebo aktivní'], 400);
        }

        $booking->setStatus(Booking::STATUS_COMPLETED);
        $booking->setUpdatedAt(new \DateTimeImmutable());

        $this->entityManager->flush();

        return new JsonResponse([
            'id' => $booking->getId(),
            'status' => $booking->getStatus(),
            'message' => 'Rezervace byla dokončena',
        ]);
    }

    #[Route('/pujcovani/{id}/zrusit', name: 'booking_cancel', methods: ['PUT'])]
    #[IsGranted('ROLE_USER')]
    public function cancel(int $id): JsonResponse
    {
        $booking = $this->bookingRepository->find($id);

        if (!$booking) {
            return new JsonResponse(['error' => 'Rezervace nebyla nalezena'], 404);
        }

        // Zkontroluj, jestli je uživatel žadatel
        if ($booking->getBorrower() !== $this->getUser()) {
            return new JsonResponse(['error' => 'Nemáte oprávnění zrušit tuto rezervaci'], 403);
        }

        if (!in_array($booking->getStatus(), [Booking::STATUS_PENDING, Booking::STATUS_APPROVED])) {
            return new JsonResponse(['error' => 'Rezervaci lze zrušit pouze ve stavu čekající nebo schválená'], 400);
        }

        $booking->setStatus(Booking::STATUS_CANCELLED);
        $booking->setUpdatedAt(new \DateTimeImmutable());

        $this->entityManager->flush();

        return new JsonResponse([
            'id' => $booking->getId(),
            'status' => $booking->getStatus(),
            'message' => 'Rezervace byla zrušena',
        ]);
    }
}
