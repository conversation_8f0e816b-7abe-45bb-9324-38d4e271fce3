FROM php:8.2-cli-alpine

# Instalace systémových závislostí
RUN apk add --no-cache \
    git \
    curl \
    libzip-dev \
    zip \
    unzip \
    postgresql-dev \
    autoconf \
    gcc \
    g++ \
    make \
    && docker-php-ext-configure pgsql -with-pgsql=/usr/local/pgsql \
    && docker-php-ext-install pdo pdo_pgsql pgsql zip \
    && rm -rf /var/cache/apk/*

# Instalace Redis extension
RUN pecl install redis && docker-php-ext-enable redis

# Instalace Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Nastavení pracovního adresáře
WORKDIR /var/www/html

# Kopírování composer souborů
COPY composer.json ./

# Instalace PHP závislostí
RUN composer install --no-scripts --no-autoloader --no-dev --optimize-autoloader

# <PERSON>p<PERSON>rov<PERSON><PERSON> aplikace
COPY . .

# Dokončení Composer instalace
RUN composer dump-autoload --optimize

# Vytvoření potřebných adresářů
RUN mkdir -p var/cache var/log config/jwt public/uploads \
    && chmod -R 777 var/ config/jwt/ public/uploads/

# Expose port
EXPOSE 8000

# Spuštění Symfony serveru
CMD ["php", "-S", "0.0.0.0:8000", "-t", "public"]
