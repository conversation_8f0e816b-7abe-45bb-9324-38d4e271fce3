{"name": "<PERSON>j<PERSON>vna-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0", "build": "next build", "build:static": "BUILD_MODE=static next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"next": "14.0.4", "react": "^18", "react-dom": "^18", "typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "tailwindcss": "^3.3.0", "autoprefixer": "^10.0.1", "postcss": "^8", "axios": "^1.6.0", "react-hook-form": "^7.48.0", "react-query": "^3.39.0", "js-cookie": "^3.0.5", "@types/js-cookie": "^3.0.6", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.0.4", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0"}}