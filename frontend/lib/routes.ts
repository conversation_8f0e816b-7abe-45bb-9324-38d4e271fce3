// Konstanty pro routy aplikace
export const ROUTES = {
  // <PERSON><PERSON><PERSON><PERSON> stránky
  HOME: '/',

  // Autentizace
  AUTH: {
    LOGIN: '/prihlaseni',
    REGISTER: '/registrace',
    FORGOT_PASSWORD: '/zapomenute-heslo',
  },

  // Předměty
  ITEMS: {
    LIST: '/predmety',
    CREATE: '/predmety/novy',
    DETAIL: (id: string | number) => `/predmety/${id}`,
    EDIT: (id: string | number) => `/predmety/${id}/upravit`,
  },

  // Uživatelské stránky
  USER: {
    DASHBOARD: '/nastenka',
    PROFILE: '/profil',
    MY_ITEMS: '/moje-predmety',
    BOOKINGS: '/pujcovani',
    NOTIFICATIONS: '/oznameni',
  },

  // Statické stránky
  STATIC: {
    ABOUT: '/o-nas',
    HOW_IT_WORKS: '/jak-to-funguje',
    SAFETY: '/bezpecnost',
    HELP: '/napoveda',
    CONTACT: '/kontakt',
    TERMS: '/podminky',
    PRIVACY: '/ochrana-udaju',
    FAQ: '/faq',
    BLOG: '/blog',
  },

  // Kategorie
  CATEGORIES: '/kategorie',
} as const;

// Helper funkce pro kontrolu autentizačních stránek
export const isAuthPage = (pathname: string): boolean => {
  return pathname === ROUTES.AUTH.LOGIN || pathname === ROUTES.AUTH.REGISTER;
};

// Helper funkce pro kontrola, zda je stránka veřejná (nepotřebuje přihlášení)
export const isPublicPage = (pathname: string): boolean => {
  const publicPages = [
    ROUTES.HOME,
    ROUTES.AUTH.LOGIN,
    ROUTES.AUTH.REGISTER,
    ROUTES.AUTH.FORGOT_PASSWORD,
    ROUTES.ITEMS.LIST,
    ROUTES.STATIC.ABOUT,
    ROUTES.STATIC.HOW_IT_WORKS,
    ROUTES.STATIC.SAFETY,
    ROUTES.STATIC.HELP,
    ROUTES.STATIC.CONTACT,
    ROUTES.STATIC.TERMS,
    ROUTES.STATIC.PRIVACY,
    ROUTES.STATIC.FAQ,
    ROUTES.CATEGORIES,
  ];

  return (
    publicPages.includes(pathname as any) ||
    pathname.startsWith(ROUTES.ITEMS.LIST + '/')
  );
};

// Helper funkce pro kontrolu, zda stránka vyžaduje přihlášení
export const requiresAuth = (pathname: string): boolean => {
  return !isPublicPage(pathname);
};
