import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import Cookies from 'js-cookie';
import { apiClient } from '@/lib/api';
import { User, LoginData, RegisterData } from '@/types';

export function useAuth() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const queryClient = useQueryClient();

  useEffect(() => {
    const token = Cookies.get('auth_token');
    setIsAuthenticated(!!token);
    setIsInitialized(true);
  }, []);

  const {
    data: user,
    isLoading: isLoadingUser,
    error: userError,
  } = useQuery<User>('user', () => apiClient.getProfile(), {
    enabled: isAuthenticated && isInitialized,
    retry: false,
    onError: () => {
      setIsAuthenticated(false);
      Cookies.remove('auth_token');
      Cookies.remove('refresh_token');
    },
  });

  const loginMutation = useMutation(
    (data: LoginData) => apiClient.login(data),
    {
      onSuccess: (data) => {
        setIsAuthenticated(true);
        queryClient.setQueryData('user', data.user);
        queryClient.invalidateQueries('user');
      },
    }
  );

  const registerMutation = useMutation((data: RegisterData) =>
    apiClient.register(data)
  );

  const logout = () => {
    apiClient.logout();
    setIsAuthenticated(false);
    queryClient.clear();
  };

  // Skutečná autentizace je pouze když máme token a úspěšně načtený uživatel
  const isReallyAuthenticated = isAuthenticated && user && !userError;

  return {
    user,
    isAuthenticated: isAuthenticated, // Vrať základní stav tokenu, ne složitou logiku
    isReallyAuthenticated, // Přidej novou property pro složitou logiku
    isLoadingUser: isLoadingUser || !isInitialized,
    userError,
    login: loginMutation.mutateAsync,
    register: registerMutation.mutateAsync,
    logout,
    isLoggingIn: loginMutation.isLoading,
    isRegistering: registerMutation.isLoading,
    loginError: loginMutation.error,
    registerError: registerMutation.error,
  };
}
