'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { apiClient } from '@/lib/api';
import { RegisterData, RegisterFormData } from '@/types';
import Layout from '@/components/layout/Layout';
import { ROUTES } from '@/lib/routes';
import AddressAutocomplete from '@/components/ui/AddressAutocomplete';

export default function RegisterPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [addressInput, setAddressInput] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm<RegisterFormData>();

  const password = watch('password');

  const handleAddressSelect = (address: any) => {
    console.log('Address selected:', address);

    // Použijeme setTimeout pro zaj<PERSON>t<PERSON>n<PERSON>, že se setValue provede po render cyklu
    setTimeout(() => {
      setValue('address.street', address.street, {
        shouldValidate: true,
        shouldDirty: true,
      });
      setValue('address.city', address.city, {
        shouldValidate: true,
        shouldDirty: true,
      });
      setValue('address.zipCode', address.zipCode, {
        shouldValidate: true,
        shouldDirty: true,
      });
      setValue('address.latitude', address.latitude, {
        shouldValidate: true,
        shouldDirty: true,
      });
      setValue('address.longitude', address.longitude, {
        shouldValidate: true,
        shouldDirty: true,
      });

      console.log('Values set:', {
        street: address.street,
        city: address.city,
        zipCode: address.zipCode,
        latitude: address.latitude,
        longitude: address.longitude,
      });
    }, 100);

    setAddressInput(address.street + ', ' + address.city);
  };

  const onSubmit = async (data: RegisterFormData) => {
    setIsLoading(true);
    try {
      // Odstraníme confirmPassword a terms před odesláním
      const { confirmPassword, terms, ...registerData } = data;
      await apiClient.register(registerData);
      toast.success('Účet byl úspěšně vytvořen! Zkontrolujte svůj email.');
      router.push(ROUTES.AUTH.LOGIN);
    } catch (error: any) {
      const errorMessage = error.response?.data?.violations
        ? error.response.data.violations.map((v: any) => v.message).join(', ')
        : error.response?.data?.message || 'Chyba při registraci';
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Layout>
      <div className="flex min-h-screen flex-col justify-center bg-gray-50 py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <Link href={ROUTES.HOME} className="flex justify-center">
            <h1 className="text-3xl font-bold text-primary-600">Pujčovna</h1>
          </Link>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Vytvoření nového účtu
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Nebo{' '}
            <Link
              href={ROUTES.AUTH.LOGIN}
              className="font-medium text-primary-600 hover:text-primary-500"
            >
              se přihlaste do existujícího účtu
            </Link>
          </p>
        </div>

        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white px-4 py-8 shadow sm:rounded-lg sm:px-10">
            <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="firstName"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Jméno
                  </label>
                  <input
                    {...register('firstName', { required: 'Jméno je povinné' })}
                    type="text"
                    className="input mt-1"
                    placeholder="Jan"
                  />
                  {errors.firstName && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.firstName.message}
                    </p>
                  )}
                </div>

                <div>
                  <label
                    htmlFor="lastName"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Příjmení
                  </label>
                  <input
                    {...register('lastName', {
                      required: 'Příjmení je povinné',
                    })}
                    type="text"
                    className="input mt-1"
                    placeholder="Novák"
                  />
                  {errors.lastName && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.lastName.message}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700"
                >
                  Email
                </label>
                <input
                  {...register('email', {
                    required: 'Email je povinný',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Neplatný email',
                    },
                  })}
                  type="email"
                  className="input mt-1"
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.email.message}
                  </p>
                )}
              </div>

              <div>
                <label
                  htmlFor="phone"
                  className="block text-sm font-medium text-gray-700"
                >
                  Telefon (volitelné)
                </label>
                <input
                  {...register('phone')}
                  type="tel"
                  className="input mt-1"
                  placeholder="+420 123 456 789"
                />
              </div>

              <div>
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700"
                >
                  Heslo
                </label>
                <input
                  {...register('password', {
                    required: 'Heslo je povinné',
                    minLength: {
                      value: 8,
                      message: 'Heslo musí mít alespoň 8 znaků',
                    },
                  })}
                  type="password"
                  className="input mt-1"
                  placeholder="••••••••"
                />
                {errors.password && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.password.message}
                  </p>
                )}
              </div>

              <div>
                <label
                  htmlFor="confirmPassword"
                  className="block text-sm font-medium text-gray-700"
                >
                  Potvrzení hesla
                </label>
                <input
                  {...register('confirmPassword', {
                    required: 'Potvrzení hesla je povinné',
                    validate: (value) =>
                      value === password || 'Hesla se neshodují',
                  })}
                  type="password"
                  className="input mt-1"
                  placeholder="••••••••"
                />
                {errors.confirmPassword && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.confirmPassword.message}
                  </p>
                )}
              </div>

              <div className="space-y-4">
                <h3 className="text-sm font-medium text-gray-700">Adresa</h3>

                {/* Našeptávač adres */}
                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700">
                    Vyhledat adresu
                  </label>
                  <AddressAutocomplete
                    onAddressSelect={handleAddressSelect}
                    value={addressInput}
                    onChange={setAddressInput}
                    placeholder="Začněte psát adresu..."
                  />
                </div>

                <div>
                  <input
                    {...register('address.street', {
                      required: 'Ulice je povinná',
                    })}
                    type="text"
                    className="input"
                    placeholder="Ulice a číslo popisné"
                  />
                  {errors.address?.street && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.address.street.message}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <input
                      {...register('address.city', {
                        required: 'Město je povinné',
                      })}
                      type="text"
                      className="input"
                      placeholder="Město"
                    />
                    {errors.address?.city && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.address.city.message}
                      </p>
                    )}
                  </div>
                  <div>
                    <input
                      {...register('address.zipCode', {
                        required: 'PSČ je povinné',
                      })}
                      type="text"
                      className="input"
                      placeholder="PSČ"
                    />
                    {errors.address?.zipCode && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.address.zipCode.message}
                      </p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <input
                      {...register('address.latitude', {
                        required: 'Zeměpisná šířka je povinná',
                        valueAsNumber: true,
                      })}
                      type="number"
                      step="any"
                      className="input"
                      placeholder="Zeměpisná šířka"
                    />
                    {errors.address?.latitude && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.address.latitude.message}
                      </p>
                    )}
                  </div>
                  <div>
                    <input
                      {...register('address.longitude', {
                        required: 'Zeměpisná délka je povinná',
                        valueAsNumber: true,
                      })}
                      type="number"
                      step="any"
                      className="input"
                      placeholder="Zeměpisná délka"
                    />
                    {errors.address?.longitude && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.address.longitude.message}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex items-center">
                <input
                  id="terms"
                  {...register('terms', {
                    required: 'Musíte souhlasit s podmínkami',
                  })}
                  type="checkbox"
                  className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <label
                  htmlFor="terms"
                  className="ml-2 block text-sm text-gray-900"
                >
                  Souhlasím s{' '}
                  <Link
                    href={ROUTES.STATIC.TERMS}
                    className="text-primary-600 hover:text-primary-500"
                  >
                    obchodními podmínkami
                  </Link>{' '}
                  a{' '}
                  <Link
                    href={ROUTES.STATIC.PRIVACY}
                    className="text-primary-600 hover:text-primary-500"
                  >
                    zásadami ochrany osobních údajů
                  </Link>
                </label>
              </div>
              {errors.terms && (
                <p className="text-sm text-red-600">{errors.terms.message}</p>
              )}

              <div>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="btn btn-primary w-full disabled:cursor-not-allowed disabled:opacity-50"
                >
                  {isLoading ? 'Vytvářím účet...' : 'Vytvořit účet'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </Layout>
  );
}
