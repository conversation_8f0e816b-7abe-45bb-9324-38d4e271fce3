'use client';

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { apiClient } from '@/lib/api';
import { LoginData } from '@/types';
import Layout from '@/components/layout/Layout';
import { ROUTES } from '@/lib/routes';

export default function LoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginData>();

  const onSubmit = async (data: LoginData) => {
    setIsLoading(true);
    try {
      await apiClient.login(data);
      toast.success('Úspěšně přihlášen!');

      // Přesměruj zpět na původní stránku nebo na dashboard
      const redirectTo = searchParams.get('redirect') || ROUTES.USER.DASHBOARD;
      router.push(redirectTo);
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Chyba při přihlašování');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Layout>
      <div className="flex min-h-screen flex-col justify-center bg-gray-50 py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <Link href={ROUTES.HOME} className="flex justify-center">
            <h1 className="text-3xl font-bold text-primary-600">Pujčovna</h1>
          </Link>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Přihlášení do účtu
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Nebo{' '}
            <Link
              href={ROUTES.AUTH.REGISTER}
              className="font-medium text-primary-600 hover:text-primary-500"
            >
              si vytvořte nový účet
            </Link>
          </p>
        </div>

        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white px-4 py-8 shadow sm:rounded-lg sm:px-10">
            <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700"
                >
                  Email
                </label>
                <div className="mt-1">
                  <input
                    {...register('email', {
                      required: 'Email je povinný',
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: 'Neplatný email',
                      },
                    })}
                    type="email"
                    className="input"
                    placeholder="<EMAIL>"
                  />
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.email.message}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700"
                >
                  Heslo
                </label>
                <div className="mt-1">
                  <input
                    {...register('password', {
                      required: 'Heslo je povinné',
                      minLength: {
                        value: 6,
                        message: 'Heslo musí mít alespoň 6 znaků',
                      },
                    })}
                    type="password"
                    className="input"
                    placeholder="••••••••"
                  />
                  {errors.password && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.password.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    id="remember-me"
                    name="remember-me"
                    type="checkbox"
                    className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <label
                    htmlFor="remember-me"
                    className="ml-2 block text-sm text-gray-900"
                  >
                    Zapamatovat si mě
                  </label>
                </div>

                <div className="text-sm">
                  <Link
                    href={ROUTES.AUTH.FORGOT_PASSWORD}
                    className="font-medium text-primary-600 hover:text-primary-500"
                  >
                    Zapomněli jste heslo?
                  </Link>
                </div>
              </div>

              <div>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="btn btn-primary w-full disabled:cursor-not-allowed disabled:opacity-50"
                >
                  {isLoading ? 'Přihlašuji...' : 'Přihlásit se'}
                </button>
              </div>
            </form>

            <div className="mt-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="bg-white px-2 text-gray-500">
                    Nebo pokračujte s
                  </span>
                </div>
              </div>

              <div className="mt-6 grid grid-cols-2 gap-3">
                <button className="inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-500 shadow-sm hover:bg-gray-50">
                  Google
                </button>
                <button className="inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-500 shadow-sm hover:bg-gray-50">
                  Facebook
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
