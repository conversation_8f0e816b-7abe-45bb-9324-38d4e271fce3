'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { User, Mail, MapPin, Phone, Edit, Save, X } from 'lucide-react';
import toast from 'react-hot-toast';
import { useAuth } from '@/hooks/useAuth';
import { useProfile, useUpdateProfile } from '@/hooks/useProfile';
import Layout from '@/components/layout/Layout';
import { ROUTES } from '@/lib/routes';
import AddressAutocomplete from '@/components/ui/AddressAutocomplete';

export default function ProfilPage() {
  const router = useRouter();
  const { isAuthenticated, isLoadingUser } = useAuth();
  const [editing, setEditing] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: {
      street: '',
      city: '',
      zipCode: '',
      latitude: 0,
      longitude: 0,
    },
  });
  const [addressInput, setAddressInput] = useState('');

  // Použij React Query hooks místo přímého volání API
  const { data: profile, isLoading: loading } = useProfile();
  const updateProfileMutation = useUpdateProfile();

  // Přesměruj nepřihlášené uživatele
  useEffect(() => {
    if (!isLoadingUser && !isAuthenticated) {
      router.push(ROUTES.AUTH.LOGIN);
      return;
    }
  }, [isAuthenticated, isLoadingUser, router]);

  // Aktualizuj formData když se načte profil
  useEffect(() => {
    if (profile) {
      setFormData({
        firstName: profile.firstName || '',
        lastName: profile.lastName || '',
        email: profile.email || '',
        phone: profile.phone || '',
        address: {
          street: profile.address?.street || '',
          city: profile.address?.city || '',
          zipCode: profile.address?.zipCode || '',
          latitude: profile.address?.latitude || 0,
          longitude: profile.address?.longitude || 0,
        },
      });
    }
  }, [profile]);

  const handleSave = async () => {
    try {
      await updateProfileMutation.mutateAsync(formData);
      setEditing(false);
      toast.success('Profil byl úspěšně aktualizován');
    } catch (error: any) {
      console.error('Chyba při ukládání profilu:', error);
      toast.error(
        error.response?.data?.message || 'Chyba při ukládání profilu'
      );
    }
  };

  const handleCancel = () => {
    if (profile) {
      setFormData({
        firstName: profile.firstName || '',
        lastName: profile.lastName || '',
        email: profile.email || '',
        phone: profile.phone || '',
        address: {
          street: profile.address?.street || '',
          city: profile.address?.city || '',
          zipCode: profile.address?.zipCode || '',
          latitude: profile.address?.latitude || 0,
          longitude: profile.address?.longitude || 0,
        },
      });
    }
    setEditing(false);
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;

    if (name.startsWith('address.')) {
      const addressField = name.split('.')[1];
      setFormData((prev) => ({
        ...prev,
        address: {
          ...prev.address,
          [addressField]:
            addressField === 'latitude' || addressField === 'longitude'
              ? parseFloat(value) || 0
              : value,
        },
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleAddressSelect = (address: any) => {
    setFormData((prev) => ({
      ...prev,
      address: {
        street: address.street,
        city: address.city,
        zipCode: address.zipCode,
        latitude: address.latitude,
        longitude: address.longitude,
      },
    }));
    setAddressInput(address.street + ', ' + address.city);
  };

  // Zobrazíme loading během ověřování uživatele
  if (isLoadingUser) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mx-auto h-32 w-32 animate-spin rounded-full border-b-2 border-primary-600"></div>
          <p className="mt-4 text-gray-600">Načítám...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mx-auto h-32 w-32 animate-spin rounded-full border-b-2 border-primary-600"></div>
          <p className="mt-4 text-gray-600">Načítám profil...</p>
        </div>
      </div>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-4xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="overflow-hidden rounded-lg bg-white shadow-sm">
            {/* Header */}
            <div className="bg-gradient-to-r from-primary-500 to-primary-600 px-6 py-8">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex h-20 w-20 items-center justify-center rounded-full bg-white">
                    <User className="h-10 w-10 text-primary-600" />
                  </div>
                  <div className="text-white">
                    <h1 className="text-2xl font-bold">
                      {profile?.firstName} {profile?.lastName}
                    </h1>
                    <p className="text-primary-100">
                      Člen od{' '}
                      {new Date(profile?.createdAt || '').toLocaleDateString(
                        'cs-CZ'
                      )}
                    </p>
                  </div>
                </div>

                <div className="flex space-x-2">
                  {editing ? (
                    <>
                      <button
                        onClick={handleSave}
                        className="flex items-center space-x-2 rounded-md bg-white px-4 py-2 text-primary-600 hover:bg-gray-50"
                      >
                        <Save className="h-4 w-4" />
                        <span>Uložit</span>
                      </button>
                      <button
                        onClick={handleCancel}
                        className="flex items-center space-x-2 rounded-md bg-primary-700 px-4 py-2 text-white hover:bg-primary-800"
                      >
                        <X className="h-4 w-4" />
                        <span>Zrušit</span>
                      </button>
                    </>
                  ) : (
                    <button
                      onClick={() => setEditing(true)}
                      className="flex items-center space-x-2 rounded-md bg-white px-4 py-2 text-primary-600 hover:bg-gray-50"
                    >
                      <Edit className="h-4 w-4" />
                      <span>Upravit</span>
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="p-6">
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                {/* Osobní údaje */}
                <div className="space-y-4">
                  <h2 className="mb-4 text-lg font-semibold text-gray-900">
                    Osobní údaje
                  </h2>

                  <div>
                    <label className="mb-1 block text-sm font-medium text-gray-700">
                      Jméno
                    </label>
                    {editing ? (
                      <input
                        type="text"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleChange}
                        className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-primary-500"
                      />
                    ) : (
                      <p className="text-gray-900">{profile?.firstName}</p>
                    )}
                  </div>

                  <div>
                    <label className="mb-1 block text-sm font-medium text-gray-700">
                      Příjmení
                    </label>
                    {editing ? (
                      <input
                        type="text"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleChange}
                        className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-primary-500"
                      />
                    ) : (
                      <p className="text-gray-900">{profile?.lastName}</p>
                    )}
                  </div>

                  <div>
                    <label className="mb-1 block text-sm font-medium text-gray-700">
                      <Mail className="mr-1 inline h-4 w-4" />
                      Email
                    </label>
                    {editing ? (
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-primary-500"
                      />
                    ) : (
                      <p className="text-gray-900">{profile?.email}</p>
                    )}
                  </div>

                  <div>
                    <label className="mb-1 block text-sm font-medium text-gray-700">
                      <Phone className="mr-1 inline h-4 w-4" />
                      Telefon
                    </label>
                    {editing ? (
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-primary-500"
                      />
                    ) : (
                      <p className="text-gray-900">
                        {profile?.phone || 'Neuvedeno'}
                      </p>
                    )}
                  </div>
                </div>

                {/* Adresa */}
                <div className="space-y-4">
                  <h2 className="mb-4 text-lg font-semibold text-gray-900">
                    <MapPin className="mr-1 inline h-5 w-5" />
                    Adresa
                  </h2>

                  {editing && (
                    <div>
                      <label className="mb-2 block text-sm font-medium text-gray-700">
                        Vyhledat adresu
                      </label>
                      <AddressAutocomplete
                        onAddressSelect={handleAddressSelect}
                        value={addressInput}
                        onChange={setAddressInput}
                        placeholder="Začněte psát adresu..."
                      />
                    </div>
                  )}

                  <div>
                    <label className="mb-1 block text-sm font-medium text-gray-700">
                      Ulice
                    </label>
                    {editing ? (
                      <input
                        type="text"
                        name="address.street"
                        value={formData.address.street}
                        onChange={handleChange}
                        className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-primary-500"
                      />
                    ) : (
                      <p className="text-gray-900">
                        {profile?.address?.street || 'Neuvedeno'}
                      </p>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="mb-1 block text-sm font-medium text-gray-700">
                        Město
                      </label>
                      {editing ? (
                        <input
                          type="text"
                          name="address.city"
                          value={formData.address.city}
                          onChange={handleChange}
                          className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-primary-500"
                        />
                      ) : (
                        <p className="text-gray-900">
                          {profile?.address?.city || 'Neuvedeno'}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="mb-1 block text-sm font-medium text-gray-700">
                        PSČ
                      </label>
                      {editing ? (
                        <input
                          type="text"
                          name="address.zipCode"
                          value={formData.address.zipCode}
                          onChange={handleChange}
                          className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-primary-500"
                        />
                      ) : (
                        <p className="text-gray-900">
                          {profile?.address?.zipCode || 'Neuvedeno'}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
