'use client';

import { useEffect, useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { ArrowLeft, Package, DollarSign, FileText, Tag } from 'lucide-react';
import toast from 'react-hot-toast';
import { useAuth } from '@/hooks/useAuth';
import { useItem, useUpdateItem } from '@/hooks/useItems';
import { useCategories } from '@/hooks/useCategories';
import Layout from '@/components/layout/Layout';
import { ROUTES } from '@/lib/routes';
import Button from '@/components/ui/Button';

interface EditItemFormData {
  title: string;
  description: string;
  categoryId: number;
  pricePerDay: number;
  deposit?: number;
  condition: 'excellent' | 'good' | 'fair' | 'poor';
}

export default function EditItemPage() {
  const router = useRouter();
  const params = useParams();
  const { isAuthenticated, isLoadingUser, user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  const itemId = Number(params.id);
  const { data: item, isLoading: isLoadingItem } = useItem(itemId);
  const updateItemMutation = useUpdateItem();
  const { data: categories = [], isLoading: isLoadingCategories } =
    useCategories();

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<EditItemFormData>();

  // Přesměruj nepřihlášené uživatele
  useEffect(() => {
    if (!isLoadingUser && !isAuthenticated) {
      router.push(
        `${ROUTES.AUTH.LOGIN}?redirect=${encodeURIComponent(`/predmety/${itemId}/upravit`)}`
      );
    }
  }, [isAuthenticated, isLoadingUser, router, itemId]);

  // Předvyplň formulář po načtení dat
  useEffect(() => {
    if (item) {
      setValue('title', item.title || '');
      setValue('description', item.description || '');
      setValue('categoryId', item.category?.id || '');
      setValue('pricePerDay', item.pricePerDay || item.dailyPrice || 0);
      setValue('deposit', item.deposit || 0);
      setValue('condition', item.condition || 'excellent');
    }
  }, [item, setValue]);

  if (isLoadingUser || isLoadingItem || isLoadingCategories) {
    return (
      <Layout>
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="mx-auto h-32 w-32 animate-spin rounded-full border-b-2 border-primary-600"></div>
            <p className="mt-4 text-gray-600">Načítám...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  if (!item) {
    return (
      <Layout>
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <h1 className="mb-4 text-2xl font-bold text-gray-900">
              Předmět nebyl nalezen
            </h1>
            <button
              onClick={() => router.push(ROUTES.ITEMS.LIST)}
              className="rounded-md bg-primary-600 px-4 py-2 text-white hover:bg-primary-700"
            >
              Zpět na seznam
            </button>
          </div>
        </div>
      </Layout>
    );
  }

  // Kontrola vlastnictví
  if (user && item.owner?.id !== user.id) {
    return (
      <Layout>
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <h1 className="mb-4 text-2xl font-bold text-gray-900">
              Nemáte oprávnění upravit tento předmět
            </h1>
            <button
              onClick={() => router.push(ROUTES.ITEMS.LIST)}
              className="rounded-md bg-primary-600 px-4 py-2 text-white hover:bg-primary-700"
            >
              Zpět na seznam
            </button>
          </div>
        </div>
      </Layout>
    );
  }

  const onSubmit = async (data: EditItemFormData) => {
    setIsLoading(true);
    try {
      const itemData = {
        title: data.title,
        description: data.description,
        categoryId: data.categoryId,
        pricePerDay: data.pricePerDay,
        deposit: data.deposit || 0,
        condition: data.condition,
      };
      await updateItemMutation.mutateAsync({ id: itemId, data: itemData });
      toast.success('Předmět byl úspěšně upraven!');
      router.push(ROUTES.USER.DASHBOARD);
    } catch (error: any) {
      console.error('Chyba při úpravě předmětu:', error);
      toast.error(error.response?.data?.message || 'Chyba při úpravě předmětu');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-2xl px-4 py-8 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <button
              onClick={() => router.back()}
              className="mb-4 flex items-center text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="mr-2 h-5 w-5" />
              Zpět
            </button>
            <h1 className="text-3xl font-bold text-gray-900">
              Upravit předmět
            </h1>
            <p className="mt-2 text-gray-600">Upravte informace o předmětu.</p>
          </div>

          {/* Form */}
          <div className="rounded-lg bg-white p-6 shadow-sm">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Název */}
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  <Package className="mr-1 inline h-4 w-4" />
                  Název předmětu *
                </label>
                <input
                  {...register('title', {
                    required: 'Název je povinný',
                    minLength: {
                      value: 3,
                      message: 'Název musí mít alespoň 3 znaky',
                    },
                  })}
                  type="text"
                  className="input"
                  placeholder="Např. Vrtačka Bosch Professional"
                />
                {errors.title && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.title.message}
                  </p>
                )}
              </div>

              {/* Popis */}
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  <FileText className="mr-1 inline h-4 w-4" />
                  Popis *
                </label>
                <textarea
                  {...register('description', {
                    required: 'Popis je povinný',
                    minLength: {
                      value: 10,
                      message: 'Popis musí mít alespoň 10 znaků',
                    },
                  })}
                  rows={4}
                  className="input"
                  placeholder="Popište váš předmět, jeho stav, co je v balení, případné omezení použití..."
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.description.message}
                  </p>
                )}
              </div>

              {/* Kategorie */}
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  <Tag className="mr-1 inline h-4 w-4" />
                  Kategorie *
                </label>
                <select
                  {...register('categoryId', {
                    required: 'Kategorie je povinná',
                    valueAsNumber: true,
                  })}
                  className="input"
                >
                  <option value="">Vyberte kategorii</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
                {errors.categoryId && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.categoryId.message}
                  </p>
                )}
              </div>

              {/* Cena a záloha */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700">
                    <DollarSign className="mr-1 inline h-4 w-4" />
                    Cena za den (Kč) *
                  </label>
                  <input
                    {...register('pricePerDay', {
                      required: 'Cena je povinná',
                      valueAsNumber: true,
                      min: {
                        value: 1,
                        message: 'Cena musí být alespoň 1 Kč',
                      },
                    })}
                    type="number"
                    min="1"
                    step="1"
                    className="input"
                    placeholder="100"
                  />
                  {errors.pricePerDay && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.pricePerDay.message}
                    </p>
                  )}
                </div>

                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700">
                    Záloha (Kč)
                  </label>
                  <input
                    {...register('deposit', {
                      valueAsNumber: true,
                      min: {
                        value: 0,
                        message: 'Záloha nemůže být záporná',
                      },
                    })}
                    type="number"
                    min="0"
                    step="1"
                    className="input"
                    placeholder="500"
                  />
                  {errors.deposit && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.deposit.message}
                    </p>
                  )}
                </div>
              </div>

              {/* Stav */}
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  Stav předmětu *
                </label>
                <select
                  {...register('condition', {
                    required: 'Stav předmětu je povinný',
                  })}
                  className="input"
                >
                  <option value="">Vyberte stav</option>
                  <option value="excellent">Výborný</option>
                  <option value="good">Dobrý</option>
                  <option value="fair">Průměrný</option>
                  <option value="poor">Špatný</option>
                </select>
                {errors.condition && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.condition.message}
                  </p>
                )}
              </div>

              {/* Tlačítka */}
              <div className="flex justify-end space-x-4 pt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                  disabled={isLoading}
                >
                  Zrušit
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="min-w-[120px]"
                >
                  {isLoading ? 'Ukládám...' : 'Uložit změny'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </Layout>
  );
}
